import React, { useState } from "react";

export default function BusinessName() {
  const [name, setName] = useState("");

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        {/* Logo */}
        <div className="flex justify-center mb-4">
          <img src="/logo.png" alt="Navi Logo" className="h-8" />
        </div>
        {/* Title */}
        <h2 className="text-2xl font-semibold text-center mb-2">Business Name</h2>
        {/* Subtitle */}
        <p className="text-gray-600 text-center mb-6">
          Tell us the name of your motel or hotel.
        </p>
        {/* Input */}
        <input
          type="text"
          className="w-full border border-gray-300 rounded px-3 py-2 mb-4 focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Enter Your Motel Name"
          value={name}
          onChange={e => setName(e.target.value)}
        />
        {/* Next Button */}
        <button
          className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition"
          onClick={() => {/* handle next step */}}
        >
          Next
        </button>
      </div>
    </div>
  );
}